using System;
using System.Collections.Generic;

namespace Kal_Quests_Tracker.Models
{
    public class Quest
    {
        public string Type { get; set; }
        public object QuestId { get; set; }
        public int Level { get; set; }
        public List<string> Steps { get; set; }
        public List<string> Rewards { get; set; }

        // Additional properties for tracking
        public bool IsCompleted { get; set; }
        public string DisplayName => $"{Type} {QuestId} (Level {Level})";
        public string QuestIdString => QuestId?.ToString() ?? "";

        public Quest()
        {
            Steps = new List<string>();
            Rewards = new List<string>();
            IsCompleted = false;
        }
    }

    public class QuestData
    {
        public List<Quest> Quests { get; set; }

        public QuestData()
        {
            Quests = new List<Quest>();
        }
    }
}
