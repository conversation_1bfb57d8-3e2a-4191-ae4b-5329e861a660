@echo off
echo Building KalOnline Quest Tracker...

set FRAMEWORK_PATH=C:\Windows\Microsoft.NET\Framework64\v4.0.30319
set CSC=%FRAMEWORK_PATH%\csc.exe

if not exist "%CSC%" (
    echo .NET Framework 4.0 compiler not found!
    pause
    exit /b 1
)

cd "Kal Quests Tracker"

if not exist "bin\Debug" mkdir "bin\Debug"

echo Compiling application...
"%CSC%" /target:winexe ^
    /out:"bin\Debug\Kal Quests Tracker.exe" ^
    /reference:System.dll ^
    /reference:System.Core.dll ^
    /reference:System.Data.dll ^
    /reference:System.Drawing.dll ^
    /reference:System.Windows.Forms.dll ^
    /reference:System.Web.Extensions.dll ^
    /reference:System.Xml.dll ^
    /win32icon:"%SystemRoot%\System32\shell32.dll,1" ^
    "*.cs" "Models\*.cs"

if %ERRORLEVEL% EQU 0 (
    echo Build successful!
    echo Copying quest data...
    copy "QuestsData.json" "bin\Debug\" >nul
    echo.
    echo Application built successfully in bin\Debug\
    echo You can now run "Kal Quests Tracker.exe"
) else (
    echo Build failed!
)

pause
