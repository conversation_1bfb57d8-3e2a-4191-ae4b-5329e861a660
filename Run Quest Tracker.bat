@echo off
echo Starting KalOnline Quest Tracker...
cd "Kal Quests Tracker\bin\Debug"
if exist "Kal Quests Tracker.exe" (
    start "KalOnline Quest Tracker" "Kal Quests Tracker.exe"
) else (
    echo Application not found. Please build the project first.
    echo.
    echo To build the project:
    echo 1. Open the solution in Visual Studio
    echo 2. Build the solution (Ctrl+Shift+B)
    echo 3. Run this script again
    pause
)
