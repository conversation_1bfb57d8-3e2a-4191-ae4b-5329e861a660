# KalOnline Quest Tracker

A modern, dark-themed quest tracker application for KalOnline with comprehensive quest management and character progress tracking.

## Features

### 🎯 Quest Management
- **Complete Quest Database**: Loads all quests from QuestsData.json
- **Smart Filtering**: Filter by quest type (QUEST/EVENT) and completion status
- **Level-based Organization**: Quests sorted by level, type, and ID
- **Visual Completion Status**: Completed quests shown in green with checkmarks

### 🎨 Modern Dark UI
- **Dark Theme**: Professional dark color scheme throughout
- **Colored Text Parsing**: Automatic color coding for different elements:
  - 🔵 **NPCs and Characters** (in quotes) - Light Blue
  - 🟠 **Items** (quest items, drops) - Orange  
  - 🔴 **Monsters** (after "by") - Red
  - 🔵 **Locations** (cities, areas) - Cyan
  - 🟣 **Special Characters** (in brackets) - Magenta
  - ⚪ **Coordinates/IDs** (in parentheses) - Gray

### 👤 Character Profiles
- **Character Management**: Save/load character profiles with quest progress
- **Auto-save**: Automatically saves progress when closing the application
- **Profile Persistence**: JSON-based profile storage in Profiles folder
- **Multiple Characters**: Support for multiple character profiles

### ✅ Quest Completion Tracking
- **Double-click to Complete**: Double-click any quest to mark as completed
- **Visual Feedback**: Completed quests highlighted in green
- **Progress Persistence**: Quest completion status saved with character profile
- **Toggle Completion**: Double-click again to mark as incomplete

## How to Use

### Getting Started
1. **Launch the Application**: Run the KalOnline Quest Tracker
2. **Enter Character Name**: Type your character name in the top field
3. **Browse Quests**: Use the quest list on the left to browse available quests
4. **View Details**: Click on any quest to see detailed steps and rewards

### Managing Quest Progress
1. **Mark as Complete**: Double-click on any quest in the list to mark it as completed
2. **View Completed**: Completed quests appear in green with a ✓ checkmark
3. **Filter Options**: Use "Show Completed" checkbox to hide/show completed quests
4. **Quest Types**: Filter by "All Types", "QUEST", or "EVENT"

### Character Profiles
1. **Auto-save**: Progress is automatically saved when you close the application
2. **Manual Save**: Click "Save Profile" to manually save your progress
3. **Load Profile**: Click "Load Profile" to load a previously saved character
4. **Multiple Characters**: Create different profiles for different characters

### Quest Information Display
- **Steps**: Detailed quest steps with color-coded elements
- **Rewards**: List of quest rewards (experience, items, etc.)
- **Level Requirements**: Quest level requirements clearly displayed
- **Quest Types**: Visual distinction between regular quests and events

## File Structure
```
Kal Quests Tracker/
├── QuestsData.json          # Quest database (included)
├── Profiles/                # Character profiles (auto-created)
│   └── [CharacterName].json # Individual character progress files
├── Models/                  # Data models
├── Utils/                   # JSON parsing utilities
└── bin/Debug/              # Compiled application
```

## Technical Features
- **Custom JSON Parser**: No external dependencies required
- **Regex-based Text Parsing**: Intelligent color coding of quest text
- **Owner-drawn ListBox**: Custom rendering for quest completion status
- **Automatic Sorting**: Quests organized by level and type
- **Error Handling**: Robust error handling for file operations

## Tips
- Double-click quests to quickly mark them as complete/incomplete
- Use the quest type filter to focus on specific quest types
- Character profiles are automatically saved when you close the application
- The quest details panel shows color-coded information for easy reading
- Completed quests can be hidden using the "Show Completed" checkbox

## Setup Instructions

### Prerequisites
- Windows with .NET Framework 4.8
- Visual Studio 2019 or later (Community Edition is free)

### Building the Application

1. **Install Newtonsoft.Json Package**:
   - Open the solution in Visual Studio
   - Right-click on the project in Solution Explorer
   - Select "Manage NuGet Packages"
   - Search for "Newtonsoft.Json" and install version 13.0.3

2. **Build the Project**:
   - Press `Ctrl+Shift+B` to build the solution
   - Or go to Build → Build Solution

3. **Run the Application**:
   - Press `F5` to run with debugging
   - Or press `Ctrl+F5` to run without debugging
   - Or use the "Run Quest Tracker.bat" file after building

### Alternative: Manual Package Installation

If you can't use NuGet, download Newtonsoft.Json manually:

1. Download Newtonsoft.Json 13.0.3 from [NuGet.org](https://www.nuget.org/packages/Newtonsoft.Json/13.0.3)
2. Extract the .nupkg file (rename to .zip and extract)
3. Copy `Newtonsoft.Json.dll` from `lib/net45/` to your project's `packages/Newtonsoft.Json.13.0.3/lib/net45/` folder
4. Build the project in Visual Studio

## Troubleshooting

### "QuestsData.json file not found" Error
- Make sure `QuestsData.json` is in the same folder as the executable
- The file should be automatically copied during build

### JSON Parsing Issues
- Ensure the QuestsData.json file is valid JSON
- Check that all quotes are properly escaped
- The application now uses Newtonsoft.Json for robust parsing

### Build Errors
- Make sure Newtonsoft.Json package is properly installed
- Check that all references are resolved in Visual Studio
- Try cleaning and rebuilding the solution (Build → Clean Solution, then Build → Rebuild Solution)

## Requirements
- Windows with .NET Framework 4.8
- Visual Studio 2019+ (for building)
- Newtonsoft.Json 13.0.3 package
- QuestsData.json file in the application directory

Enjoy tracking your KalOnline quest progress! 🎮

---

## Fixed Issues in Latest Version
- ✅ **JSON Parsing**: Fixed quest step parsing that was breaking text into fragments
- ✅ **Newtonsoft.Json**: Now uses proper JSON serialization library
- ✅ **String Interpolation**: Compatible with older .NET Framework versions
- ✅ **Color Coding**: Enhanced text parsing for NPCs, items, monsters, and locations
- ✅ **Error Handling**: Improved error messages and debugging information
