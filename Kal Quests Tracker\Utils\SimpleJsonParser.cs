using System;
using System.Collections.Generic;
using System.Globalization;
using System.Text;
using System.Text.RegularExpressions;
using Kal_Quests_Tracker.Models;

namespace Kal_Quests_Tracker.Utils
{
    public static class SimpleJsonParser
    {
        public static QuestData ParseQuestData(string json)
        {
            var questData = new QuestData();
            
            // Find the Quests array
            var questsMatch = Regex.Match(json, @"""Quests""\s*:\s*\[(.*)\]", RegexOptions.Singleline);
            if (!questsMatch.Success) return questData;
            
            string questsContent = questsMatch.Groups[1].Value;
            
            // Split into individual quest objects
            var questMatches = Regex.Matches(questsContent, @"\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}", RegexOptions.Singleline);
            
            foreach (Match questMatch in questMatches)
            {
                var quest = ParseQuest(questMatch.Value);
                if (quest != null)
                {
                    questData.Quests.Add(quest);
                }
            }
            
            return questData;
        }
        
        private static Quest ParseQuest(string questJson)
        {
            var quest = new Quest();
            
            // Parse Type
            var typeMatch = Regex.Match(questJson, @"""Type""\s*:\s*""([^""]*)""");
            if (typeMatch.Success)
                quest.Type = typeMatch.Groups[1].Value;
            
            // Parse QuestId (can be string or number)
            var questIdMatch = Regex.Match(questJson, @"""QuestId""\s*:\s*(?:""([^""]*)""|(\d+))");
            if (questIdMatch.Success)
            {
                if (!string.IsNullOrEmpty(questIdMatch.Groups[1].Value))
                    quest.QuestId = questIdMatch.Groups[1].Value;
                else if (!string.IsNullOrEmpty(questIdMatch.Groups[2].Value))
                    quest.QuestId = int.Parse(questIdMatch.Groups[2].Value);
            }
            
            // Parse Level
            var levelMatch = Regex.Match(questJson, @"""Level""\s*:\s*(\d+)");
            if (levelMatch.Success)
                quest.Level = int.Parse(levelMatch.Groups[1].Value);
            
            // Parse Steps array
            var stepsMatch = Regex.Match(questJson, @"""Steps""\s*:\s*\[(.*?)\]", RegexOptions.Singleline);
            if (stepsMatch.Success)
            {
                var stepsContent = stepsMatch.Groups[1].Value;
                var stepMatches = Regex.Matches(stepsContent, @"""([^""]*)""");
                foreach (Match stepMatch in stepMatches)
                {
                    quest.Steps.Add(stepMatch.Groups[1].Value);
                }
            }
            
            // Parse Rewards array
            var rewardsMatch = Regex.Match(questJson, @"""Rewards""\s*:\s*\[(.*?)\]", RegexOptions.Singleline);
            if (rewardsMatch.Success)
            {
                var rewardsContent = rewardsMatch.Groups[1].Value;
                var rewardMatches = Regex.Matches(rewardsContent, @"""([^""]*)""");
                foreach (Match rewardMatch in rewardMatches)
                {
                    quest.Rewards.Add(rewardMatch.Groups[1].Value);
                }
            }
            
            return quest;
        }
        
        public static string SerializeCharacterProfile(CharacterProfile profile)
        {
            var sb = new StringBuilder();
            sb.AppendLine("{");
            sb.AppendLine($"  \"CharacterName\": \"{EscapeString(profile.CharacterName)}\",");
            sb.AppendLine($"  \"CreatedDate\": \"{profile.CreatedDate:yyyy-MM-ddTHH:mm:ss}\",");
            sb.AppendLine($"  \"LastModified\": \"{profile.LastModified:yyyy-MM-ddTHH:mm:ss}\",");
            sb.AppendLine("  \"CompletedQuests\": [");
            
            var quests = new List<string>(profile.CompletedQuests);
            for (int i = 0; i < quests.Count; i++)
            {
                sb.Append($"    \"{EscapeString(quests[i])}\"");
                if (i < quests.Count - 1) sb.Append(",");
                sb.AppendLine();
            }
            
            sb.AppendLine("  ]");
            sb.AppendLine("}");
            return sb.ToString();
        }
        
        public static CharacterProfile ParseCharacterProfile(string json)
        {
            var profile = new CharacterProfile();
            
            // Parse CharacterName
            var nameMatch = Regex.Match(json, @"""CharacterName""\s*:\s*""([^""]*)""");
            if (nameMatch.Success)
                profile.CharacterName = nameMatch.Groups[1].Value;
            
            // Parse CreatedDate
            var createdMatch = Regex.Match(json, @"""CreatedDate""\s*:\s*""([^""]*)""");
            if (createdMatch.Success && DateTime.TryParse(createdMatch.Groups[1].Value, out DateTime created))
                profile.CreatedDate = created;
            
            // Parse LastModified
            var modifiedMatch = Regex.Match(json, @"""LastModified""\s*:\s*""([^""]*)""");
            if (modifiedMatch.Success && DateTime.TryParse(modifiedMatch.Groups[1].Value, out DateTime modified))
                profile.LastModified = modified;
            
            // Parse CompletedQuests array
            var questsMatch = Regex.Match(json, @"""CompletedQuests""\s*:\s*\[(.*?)\]", RegexOptions.Singleline);
            if (questsMatch.Success)
            {
                var questsContent = questsMatch.Groups[1].Value;
                var questMatches = Regex.Matches(questsContent, @"""([^""]*)""");
                foreach (Match questMatch in questMatches)
                {
                    profile.CompletedQuests.Add(questMatch.Groups[1].Value);
                }
            }
            
            return profile;
        }
        
        private static string EscapeString(string str)
        {
            if (string.IsNullOrEmpty(str)) return str;
            return str.Replace("\\", "\\\\").Replace("\"", "\\\"").Replace("\n", "\\n").Replace("\r", "\\r");
        }
    }
}
